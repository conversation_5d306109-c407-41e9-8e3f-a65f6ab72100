from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Request
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
from typing import List, Optional
import os
import shutil

from app.core.database import get_db
from app.core.security import decode_access_token
from app.models.case import CaseC<PERSON>, CaseUpdate, CaseResponse, CaseWithProcessingStatus
from app.models.collection import CollectionCreate, CollectionUpdate, CollectionResponse, TreeNode
from app.models.case_file import CaseFileResponse, FileUploadResponse
from app.models.user import User
from app.services.case_service import CaseService
from app.services.collection_service import CollectionService
from app.services.file_service import FileService
from app.services.embedpdf import process_and_train, process_and_train_batch

router = APIRouter()

# Background embedding processing function
async def process_embeddings_background(file_paths: List[str], case_id: str, file_ids: List[str]):
    """Process embeddings in the background and update database incrementally"""
    from app.core.database import SessionLocal
    from app.models.case_file import CaseFile

    # Create a new database session for the background task
    db = SessionLocal()

    try:
        print(f"Background embedding process started for case {case_id}")
        print(f"Database session created: {db}")

        # Process each file individually to provide incremental progress
        for i, (file_path, file_id) in enumerate(zip(file_paths, file_ids)):
            try:
                print(f"Processing file {i+1}/{len(file_paths)}: {file_path}")
                print(f"File ID: {file_id}")

                # Process single file embedding
                namespace = f"{case_id}_{file_id}"
                print(f"Using namespace: {namespace}")
                embedding_result = await process_and_train(file_path, namespace)
                print(f"Embedding result: {embedding_result}")

                # Update individual file status
                print(f"Querying database for file ID: {file_id}")
                db_file = db.query(CaseFile).filter(CaseFile.id == file_id).first()
                print(f"Found database file: {db_file}")
                if db_file:
                    print(f"Current file status: {db_file.upload_status}")
                    if embedding_result and embedding_result.get("status") == "success":
                        db_file.upload_status = "completed"
                        print(f"File {file_path} embedding completed successfully - updating to completed")
                    else:
                        db_file.upload_status = "error"
                        error_message = "Embedding failed"
                        if embedding_result and embedding_result.get("message"):
                            error_message = embedding_result.get("message")
                        elif not embedding_result:
                            error_message = "Embedding function returned no result"
                        db_file.error_message = error_message
                        print(f"File {file_path} embedding failed: {error_message} - updating to error")

                    print(f"Committing database changes for file {file_id}")
                    db.flush()  # Ensure changes are written to database
                    db.commit()
                    db.refresh(db_file)  # Refresh to get latest state
                    print(f"Database commit successful. New status: {db_file.upload_status}")
                else:
                    print(f"ERROR: Could not find file with ID {file_id} in database")

            except Exception as file_error:
                print(f"Error processing file {file_path}: {file_error}")
                # Update file status to error
                db_file = db.query(CaseFile).filter(CaseFile.id == file_id).first()
                if db_file:
                    db_file.upload_status = "error"
                    db_file.error_message = f"Embedding failed: {str(file_error)}"
                    db.flush()
                    db.commit()
                    db.refresh(db_file)

        print(f"Background embedding process completed for case {case_id}")

    except Exception as e:
        print(f"Critical error in background embedding process: {e}")
        # Update all remaining files to error status
        for file_id in file_ids:
            db_file = db.query(CaseFile).filter(CaseFile.id == file_id).first()
            if db_file and db_file.upload_status == "embedding":
                db_file.upload_status = "error"
                db_file.error_message = f"Background processing failed: {str(e)}"
                db.commit()
    finally:
        db.close()

def get_current_user_email(authorization: str = Depends(lambda x: x.headers.get('Authorization'))):
    if not authorization or not authorization.startswith('Bearer '):
        raise HTTPException(status_code=401, detail='Invalid auth header')
    token = authorization.split(' ')[1]
    payload = decode_access_token(token)
    if not payload:
        raise HTTPException(status_code=401, detail='Invalid or expired token')
    return payload['sub']

def get_current_user_id(authorization: str = Depends(lambda x: x.headers.get('Authorization'))):
    if not authorization or not authorization.startswith('Bearer '):
        raise HTTPException(status_code=401, detail='Invalid auth header')
    token = authorization.split(' ')[1]
    payload = decode_access_token(token)
    if not payload:
        raise HTTPException(status_code=401, detail='Invalid or expired token')
    return payload['sub']

def get_user_by_id(db: Session, user_id: str) -> User:
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    return user

# Case Management Endpoints
@router.post("/cases", response_model=CaseResponse)
def create_case(
    case_data: CaseCreate,
    current_user_id: str = Depends(get_current_user_id),
    db: Session = Depends(get_db)
):
    """Create a new case"""
    try:
        return CaseService.create_case(db, case_data, current_user_id)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/cases", response_model=List[CaseResponse])
def get_cases(
    skip: int = 0,
    limit: int = 100,
    collection_id: Optional[str] = None,
    current_user_id: str = Depends(get_current_user_id),
    db: Session = Depends(get_db)
):
    """Get all cases for the current user"""
    try:
        return CaseService.get_cases(db, current_user_id, skip, limit, collection_id)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

# Get all cases with processing status
@router.get("/cases/with-status", response_model=List[CaseWithProcessingStatus])
def get_cases_with_status(
    skip: int = 0,
    limit: int = 100,
    current_user_id: str = Depends(get_current_user_id),
    db: Session = Depends(get_db)
):
    """Get all cases with document processing status"""
    try:
        return CaseService.get_all_cases_with_status(db, current_user_id, skip, limit)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/cases/{case_id}", response_model=CaseResponse)
def get_case(
    case_id: str,
    current_user_id: str = Depends(get_current_user_id),
    db: Session = Depends(get_db)
):
    """Get a specific case by ID"""
    try:
        return CaseService.get_case(db, case_id, current_user_id)
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.put("/cases/{case_id}", response_model=CaseResponse)
def update_case(
    case_id: str,
    case_data: CaseUpdate,
    current_user_id: str = Depends(get_current_user_id),
    db: Session = Depends(get_db)
):
    """Update a case"""
    try:
        return CaseService.update_case(db, case_id, case_data, current_user_id)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.delete("/cases/{case_id}")
def delete_case(
    case_id: str,
    current_user_id: str = Depends(get_current_user_id),
    db: Session = Depends(get_db)
):
    """Delete a case and all its files"""
    try:
        CaseService.delete_case(db, case_id, current_user_id)
        return {"message": "Case deleted successfully"}
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

# Collection Management Endpoints
@router.post("/collections", response_model=CollectionResponse)
def create_collection(
    collection_data: CollectionCreate,
    current_user_id: str = Depends(get_current_user_id),
    db: Session = Depends(get_db)
):
    """Create a new collection"""
    try:
        return CollectionService.create_collection(db, collection_data, current_user_id)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/collections", response_model=List[CollectionResponse])
def get_collections(
    current_user_id: str = Depends(get_current_user_id),
    db: Session = Depends(get_db)
):
    """Get all collections for the current user"""
    try:
        return CollectionService.get_collections(db, current_user_id)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/collections/{collection_id}", response_model=CollectionResponse)
def get_collection(
    collection_id: str,
    current_user_id: str = Depends(get_current_user_id),
    db: Session = Depends(get_db)
):
    """Get a specific collection by ID"""
    try:
        return CollectionService.get_collection(db, collection_id, current_user_id)
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.put("/collections/{collection_id}", response_model=CollectionResponse)
def update_collection(
    collection_id: str,
    collection_data: CollectionUpdate,
    current_user_id: str = Depends(get_current_user_id),
    db: Session = Depends(get_db)
):
    """Update a collection"""
    try:
        return CollectionService.update_collection(db, collection_id, collection_data, current_user_id)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.delete("/collections/{collection_id}")
def delete_collection(
    collection_id: str,
    current_user_id: str = Depends(get_current_user_id),
    db: Session = Depends(get_db)
):
    """Delete a collection and all its children and cases"""
    try:
        CollectionService.delete_collection(db, collection_id, current_user_id)
        return {"message": "Collection deleted successfully"}
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

# Tree Structure Endpoint
@router.get("/tree", response_model=List[TreeNode])
def get_tree_structure(
    current_user_id: str = Depends(get_current_user_id),
    db: Session = Depends(get_db)
):
    """Get the complete tree structure of collections and cases"""
    try:
        return CollectionService.get_tree_structure(db, current_user_id)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

# File Upload Endpoints
@router.post("/cases/{case_id}/files", response_model=FileUploadResponse)
async def upload_file(
    case_id: str,
    file: UploadFile = File(...),
    process_embedding: bool = True,
    current_user_id: str = Depends(get_current_user_id),
    db: Session = Depends(get_db)
):
    """Upload a single file to a case with optional immediate embedding processing"""
    try:
        result = await FileService.upload_file(db, case_id, file, current_user_id)

        # Process embeddings for PDF files if requested
        if process_embedding:
            try:
                print(f"Starting embedding process for file: {file.filename}")
                embedding_result = await process_and_train(result.file_path, f"{case_id}_{result.file_id}")
                print(f"Embedding process completed for file: {file.filename}")
                print(f"Embedding result: {embedding_result}")

                # Check if embedding was successful
                if embedding_result.get("status") == "error":
                    print(f"Embedding failed: {embedding_result.get('message')}")
                elif embedding_result.get("status") == "success":
                    print(f"Embedding successful: {embedding_result.get('message')}")
                else:
                    print(f"Embedding status: {embedding_result.get('status')}")

            except Exception as embed_error:
                print(f"Error during embedding process: {embed_error}")
                # Don't fail the upload if embedding fails, but log the error
                # The file is already uploaded successfully, so we return the result
        else:
            print(f"File uploaded without embedding processing: {file.filename}")

        return result
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Upload failed: {str(e)}")

@router.post("/cases/{case_id}/files/upload-only", response_model=List[FileUploadResponse])
async def upload_files_only(
    case_id: str,
    files: List[UploadFile] = File(...),
    current_user_id: str = Depends(get_current_user_id),
    db: Session = Depends(get_db)
):
    """Upload multiple files to a case without processing embeddings"""
    try:
        print(f"Starting upload-only batch of {len(files)} files for case {case_id}")

        # Use the FileService batch upload method (without embedding processing)
        uploaded_results = await FileService.upload_files_batch(db, case_id, files, current_user_id)

        print(f"Upload-only batch completed for case {case_id}")
        return uploaded_results

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Upload-only batch failed: {str(e)}")

@router.post("/cases/{case_id}/files/batch", response_model=List[FileUploadResponse])
async def upload_files_batch(
    case_id: str,
    files: List[UploadFile] = File(...),
    current_user_id: str = Depends(get_current_user_id),
    db: Session = Depends(get_db)
):
    """Upload multiple files to a case and process embeddings after all uploads complete"""
    try:
        print(f"Starting batch upload of {len(files)} files for case {case_id}")

        # Use the FileService batch upload method
        uploaded_results = await FileService.upload_files_batch(db, case_id, files, current_user_id)

        successful_uploads = [r for r in uploaded_results if r.status == 'completed' and r.file_path]
        print(f"All files uploaded. Starting batch embedding process for {len(successful_uploads)} successful uploads...")

        # Process embeddings for all successfully uploaded files using batch processing
        if successful_uploads:
            try:
                # Update file status to "embedding" for all successfully uploaded files
                from app.models.case_file import CaseFile
                for result in successful_uploads:
                    db_file = db.query(CaseFile).filter(CaseFile.id == result.file_id).first()
                    if db_file:
                        db_file.upload_status = "embedding"
                        db.commit()

                file_paths = [result.file_path for result in successful_uploads]
                print(f"Starting batch embedding process for files: {[result.filename for result in successful_uploads]}")
                embedding_result = await process_and_train_batch(file_paths, case_id)
                print(f"Batch embedding process completed")
                print(f"Batch embedding result: {embedding_result}")

                # Update file status based on embedding result
                final_status = "completed" if embedding_result.get("status") == "success" else "error"
                for result in successful_uploads:
                    db_file = db.query(CaseFile).filter(CaseFile.id == result.file_id).first()
                    if db_file:
                        db_file.upload_status = final_status
                        if final_status == "error":
                            db_file.error_message = embedding_result.get("message", "Embedding failed")
                        db.commit()

                # Check if batch embedding was successful
                if embedding_result.get("status") == "error":
                    print(f"Batch embedding failed: {embedding_result.get('message')}")
                elif embedding_result.get("status") == "success":
                    print(f"Batch embedding successful: {embedding_result.get('message')}")
                else:
                    print(f"Batch embedding status: {embedding_result.get('status')}")

            except Exception as embed_error:
                print(f"Error during batch embedding process: {embed_error}")
                # Update file status to error if embedding fails
                from app.models.case_file import CaseFile
                for result in successful_uploads:
                    db_file = db.query(CaseFile).filter(CaseFile.id == result.file_id).first()
                    if db_file:
                        db_file.upload_status = "error"
                        db_file.error_message = f"Embedding failed: {str(embed_error)}"
                        db.commit()

        print(f"Batch upload and embedding process completed for case {case_id}")
        return uploaded_results

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Batch upload failed: {str(e)}")

@router.post("/cases/{case_id}/files/process-embeddings")
async def process_case_embeddings(
    case_id: str,
    db: Session = Depends(get_db)
):
    """Process embeddings for all uploaded files in a case"""
    try:
        # For testing purposes, use a mock user ID
        mock_user_id = "test-user-123"

        print(f"Starting embedding process for case {case_id}")

        # Get all uploaded files for this case that need embedding processing
        from app.models.case_file import CaseFile
        uploaded_files = db.query(CaseFile).filter(
            CaseFile.case_id == case_id,
            CaseFile.upload_status == "uploaded"
        ).all()

        if not uploaded_files:
            return {"message": "No files found that need embedding processing", "processed_count": 0}

        print(f"Found {len(uploaded_files)} files to process embeddings for")

        # Update file status to "embedding" for all files
        for db_file in uploaded_files:
            db_file.upload_status = "embedding"
            db.commit()

        # Start background embedding process that updates database incrementally
        file_paths = [file.file_path for file in uploaded_files]
        file_names = [file.original_filename for file in uploaded_files]
        print(f"Starting background embedding process for files: {file_names}")

        # Import asyncio and start background task
        import asyncio

        # Start the embedding process in the background
        asyncio.create_task(process_embeddings_background(file_paths, case_id, [f.id for f in uploaded_files]))

        # Return immediately so the API doesn't block
        return {
            "message": f"Embedding processing started for {len(uploaded_files)} files",
            "processed_count": len(uploaded_files),
            "status": "started"
        }

    except Exception as e:
        print(f"Error starting embedding process: {e}")
        # Update file status to error if embedding fails to start
        from app.models.case_file import CaseFile
        uploaded_files = db.query(CaseFile).filter(
            CaseFile.case_id == case_id,
            CaseFile.upload_status.in_(["uploaded", "embedding"])
        ).all()

        for db_file in uploaded_files:
            db_file.upload_status = "error"
            db_file.error_message = f"Embedding failed to start: {str(e)}"
            db.commit()

        raise HTTPException(status_code=500, detail=f"Embedding processing failed to start: {str(e)}")

@router.get("/cases/{case_id}/debug-files")
async def debug_case_files(
    case_id: str,
    db: Session = Depends(get_db)
):
    """Debug endpoint to check file statuses"""
    try:
        from app.models.case_file import CaseFile
        files = db.query(CaseFile).filter(CaseFile.case_id == case_id).all()

        result = []
        for file in files:
            result.append({
                "id": file.id,
                "filename": file.original_filename,
                "status": file.upload_status,
                "error_message": file.error_message,
                "created_at": file.created_at,
                "updated_at": file.updated_at
            })

        return {
            "case_id": case_id,
            "total_files": len(files),
            "files": result
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Debug failed: {str(e)}")

@router.get("/cases/{case_id}/files", response_model=List[CaseFileResponse])
def get_case_files(
    case_id: str,
    db: Session = Depends(get_db)
):
    """Get all files for a case"""
    try:
        # For testing purposes, use a mock user ID
        mock_user_id = "test-user-123"
        return FileService.get_case_files(db, case_id, mock_user_id)
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.delete("/cases/{case_id}/files/{file_id}")
def delete_case_file(
    case_id: str,
    file_id: str,
    db: Session = Depends(get_db)
):
    """Delete a file from a case"""
    try:
        # For testing purposes, use a mock user ID
        mock_user_id = "test-user-123"
        FileService.delete_case_file(db, case_id, file_id, mock_user_id)
        return {"message": "File deleted successfully"}
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/files/{file_id}/download")
def download_file(
    file_id: str,
    db: Session = Depends(get_db)
):
    """Download a file by file ID"""
    try:
        # For testing purposes, use a mock user ID
        mock_user_id = "test-user-123"
        file_path = FileService.get_file_path(db, file_id, mock_user_id)

        # Get the file record to get the original filename
        from app.models.case_file import CaseFile
        file_record = db.query(CaseFile).filter(CaseFile.id == file_id).first()
        if not file_record:
            raise HTTPException(status_code=404, detail="File not found")

        return FileResponse(
            path=file_path,
            filename=file_record.original_filename,
            media_type='application/octet-stream'
        )
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

# Test endpoint for file upload
@router.post("/test-upload")
async def test_upload(file: UploadFile = File(...)):
    """Test endpoint for file upload"""
    try:
        # Basic validation
        if not file.content_type == "application/pdf":
            raise HTTPException(status_code=400, detail="Only PDF files are allowed")

        if file.size > 50 * 1024 * 1024:
            raise HTTPException(status_code=400, detail="File size must be less than 50MB")

        # Create test directory
        test_dir = "uploads/test"
        os.makedirs(test_dir, exist_ok=True)

        # Save file
        file_path = os.path.join(test_dir, file.filename)
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)

        return {
            "message": "File uploaded successfully",
            "filename": file.filename,
            "size": file.size,
            "content_type": file.content_type,
            "path": file_path
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Upload failed: {str(e)}")

# Move case between collections
@router.put("/cases/{case_id}/move")
async def move_case(
    case_id: str,
    request: Request,
    db: Session = Depends(get_db)
):
    """Move a case to a different collection"""

    # Debug: Check what's in the request
    content_type = request.headers.get("content-type", "")

    # Parse form data manually to debug
    form_data = await request.form()

    collection_id = form_data.get("collection_id")

    # Handle empty string as None (move to root)
    if collection_id == '':
        collection_id = None

    try:
        # For testing purposes, use a mock user ID
        mock_user_id = "test-user-123"
        result = CaseService.move_case(db, case_id, collection_id, mock_user_id)
        return {"message": "Case moved successfully"}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")
