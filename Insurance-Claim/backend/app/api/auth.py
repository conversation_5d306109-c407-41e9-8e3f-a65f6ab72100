from fastapi import APIRouter, Depends, HTTPException, Request
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.models.user import UserCreate, UserResponse
from app.services.user_service import UserService
from app.core.security import create_access_token
import uuid

router = APIRouter()

@router.post('/register')
def register(user: UserCreate, request: Request, db: Session = Depends(get_db)):
    """Register a new user"""
    try:
        # Check if user already exists
        existing_user = db.query(User).filter(User.email == user.email).first()
        if existing_user:
            raise HTTPException(status_code=400, detail="User already exists")
        
        # Create new user
        user_id = str(uuid.uuid4())
        db_user = User(
            id=user_id,
            email=user.email,
            hashed_password=UserService.get_password_hash(user.password),
            first_name=user.first_name,
            last_name=user.last_name,
            is_active=True,
            is_verified=True  # For development, auto-verify users
        )
        
        db.add(db_user)
        db.commit()
        db.refresh(db_user)
        
        # Create access token
        access_token = create_access_token(data={"sub": user.id})
        
        return {
            "message": "User registered successfully",
            "access_token": access_token,
            "token_type": "bearer",
            "user": UserResponse(
                id=db_user.id,
                email=db_user.email,
                first_name=db_user.first_name,
                last_name=db_user.last_name,
                is_active=db_user.is_active,
                is_verified=db_user.is_verified
            )
        }
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Registration failed: {str(e)}")

@router.post('/login')
def login(user: UserCreate, db: Session = Depends(get_db)):
    """Login user"""
    try:
        # Find user by email
        db_user = db.query(User).filter(User.email == user.email).first()
        if not db_user:
            raise HTTPException(status_code=401, detail="Invalid credentials")
        
        # Verify password
        if not UserService.verify_password(user.password, db_user.hashed_password):
            raise HTTPException(status_code=401, detail="Invalid credentials")
        
        # Create access token
        access_token = create_access_token(data={"sub": user.id})
        
        return {
            "access_token": access_token,
            "token_type": "bearer",
            "user": UserResponse(
                id=db_user.id,
                email=db_user.email,
                first_name=db_user.first_name,
                last_name=db_user.last_name,
                is_active=db_user.is_active,
                is_verified=db_user.is_verified
            )
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Login failed: {str(e)}")

@router.get('/verify')
def verify_email(token: str, db: Session = Depends(get_db)):
    """Verify user email"""
    try:
        # For development, just return success
        return {"message": "Email verified successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Verification failed: {str(e)}")

from pydantic import BaseModel

class ForgotPasswordRequest(BaseModel):
    email: str

@router.post('/forgot-password')
def forgot_password(request: ForgotPasswordRequest, db: Session = Depends(get_db)):
    """Send password reset email"""
    try:
        # For development, just return success
        return {"message": "Password reset email sent"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Password reset failed: {str(e)}")

# Development endpoint to create test user
@router.post('/create-test-user')
def create_test_user(db: Session = Depends(get_db)):
    """Create a test user for development purposes"""
    try:
        # Check if test user already exists
        existing_user = db.query(User).filter(User.email == "<EMAIL>").first()
        if existing_user:
            return {
                "message": "Test user already exists",
                "user_id": existing_user.id,
                "email": "<EMAIL>",
                "password": "testpassword123"
            }
        
        # Create test user
        user_id = str(uuid.uuid4())
        test_user = User(
            id=user_id,
            email="<EMAIL>",
            hashed_password=UserService.get_password_hash("testpassword123"),
            first_name="Test",
            last_name="User",
            is_active=True,
            is_verified=True
        )
        
        db.add(test_user)
        db.commit()
        db.refresh(test_user)
        
        # Create access token
        access_token = create_access_token(data={"sub": test_user.id})
        
        return {
            "message": "Test user created successfully",
            "access_token": access_token,
            "token_type": "bearer",
            "user_id": user_id,
            "email": "<EMAIL>",
            "password": "testpassword123"
        }
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to create test user: {str(e)}")