from fastapi import <PERSON><PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from app.api import auth, protected, cases
from app.core.database import engine, Base

# Import all models to ensure they are registered with Base
from app.models.user import User
from app.models.case import Case
from app.models.collection import Collection
from app.models.case_file import CaseFile
from app.models.namespace import Namespace

# Create all database tables
print("Creating database tables...")
Base.metadata.create_all(bind=engine)
print("Database tables created successfully!")

app = FastAPI()

# Configure CORS to allow frontend access
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "http://*************:3000",
        "http://localhost:3001",
        "http://127.0.0.1:3001",
        "http://*************:3001/",
        "*"  # Allow all origins for development
    ],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=[
        "Accept",
        "Accept-Language",
        "Content-Language",
        "Content-Type",
        "Authorization",
        "X-Requested-With",
        "Origin",
        "Access-Control-Request-Method",
        "Access-Control-Request-Headers",
    ],
    expose_headers=["*"],
)

# Add a simple root endpoint for health check
@app.get("/")
async def root():
    return {"message": "Insurance Claim Backend API", "status": "running"}

# Add debug endpoint to list all routes
@app.get("/debug/routes")
async def list_routes():
    routes = []
    for route in app.routes:
        if hasattr(route, 'methods') and hasattr(route, 'path'):
            routes.append({
                "path": route.path,
                "methods": list(route.methods),
                "name": getattr(route, 'name', 'Unknown')
            })
    return {"routes": routes}

# Add explicit OPTIONS handler for CORS preflight
@app.options("/{path:path}")
async def options_handler(path: str):
    return {"message": "OK"}

app.include_router(auth.router, prefix="/auth", tags=["auth"])
app.include_router(protected.router, tags=["protected"])
app.include_router(cases.router, prefix="/api", tags=["cases"])
