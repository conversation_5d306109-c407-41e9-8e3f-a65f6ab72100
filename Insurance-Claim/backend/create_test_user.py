#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import SessionLocal, engine
from app.models.user import User
from app.core.security import get_password_hash
import uuid

def create_test_user():
    """Create a test user for development/testing purposes"""
    db = SessionLocal()
    
    try:
        # Check if test user already exists
        existing_user = db.query(User).filter(User.email == "<EMAIL>").first()
        if existing_user:
            print(f"Test user already exists with ID: {existing_user.id}")
            return existing_user.id
        
        # Create test user
        user_id = str(uuid.uuid4())
        test_user = User(
            id=user_id,
            email="<EMAIL>",
            hashed_password=get_password_hash("testpassword123"),
            first_name="Test",
            last_name="User",
            is_active=True,
            is_verified=True
        )
        
        db.add(test_user)
        db.commit()
        db.refresh(test_user)
        
        print(f"Test user created successfully with ID: {user_id}")
        print(f"Email: <EMAIL>")
        print(f"Password: testpassword123")
        
        return user_id
        
    except Exception as e:
        print(f"Error creating test user: {e}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    create_test_user() 