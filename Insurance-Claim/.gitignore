# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# =============================================================================
# DEPENDENCIES
# =============================================================================

# Node.js dependencies
/frontend/node_modules
/frontend/.pnp
/frontend/.pnp.js
/frontend/.yarn/install-state.gz

# Python dependencies
/backend/venv
/backend/.venv

# =============================================================================
# BUILD OUTPUTS
# =============================================================================

# Next.js build outputs
/frontend/.next/
/frontend/out/
/frontend/build

# TypeScript build info
/frontend/*.tsbuildinfo
/frontend/next-env.d.ts

# =============================================================================
# ENVIRONMENT FILES
# =============================================================================

# Frontend environment files
/frontend/.env*.local

# Backend environment files
/backend/.env
/backend/.env*.local
/backend/.env.development.local
/backend/.env.test.local
/backend/.env.production.local

# =============================================================================
# UPLOADS & DATA
# =============================================================================

# Backend uploads
/backend/uploads

# =============================================================================
# PYTHON CACHE & LOGS
# =============================================================================

# Python cache files
/backend/**/__pycache__/**
/backend/**/*.pyc
/backend/**/*.pyo
/backend/**/*.pyd
/backend/.Python

# Python logs
/backend/.log
/backend/.log.*
/backend/.log.*.*

# =============================================================================
# TESTING
# =============================================================================

# Coverage reports
/coverage

# =============================================================================
# DEBUG & LOGS
# =============================================================================

# Node.js debug logs
/frontend/npm-debug.log*
/frontend/yarn-debug.log*
/frontend/yarn-error.log*

# =============================================================================
# DEPLOYMENT
# =============================================================================

# Vercel deployment
/frontend/.vercel

# =============================================================================
# SYSTEM FILES
# =============================================================================

# macOS
.DS_Store

# SSL certificates
*.pem


