'use client';
import { useEffect, useState } from 'react';
import { Alert } from '@/components/ui/alert';
import { useAuth } from '@/lib/auth-context';

export default function Protected() {
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');
  const { token } = useAuth();

  useEffect(() => {
    if (!token) {
      setError('Not logged in');
      return;
    }
    const baseUrl = (process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000').replace(/\/$/, '');
    fetch(`${baseUrl}/protected`, {
      headers: { Authorization: `Bearer ${token}` }
    })
      .then(async res => {
        const data = await res.json();
        if (!res.ok) throw new Error(data.detail || 'Access denied');
        setMessage(data.msg);
      })
      .catch(err => setError(err.message));
  }, [token]);

  return (
    <div>
      <h2 className="text-2xl font-bold mb-4 text-blue-900">Protected Area</h2>
      <p className="mb-6 text-gray-600">This section is only accessible to authenticated users of the Insurance Litigation System.</p>
      {message && <Alert className="mt-4">{message}</Alert>}
      {error && <Alert className="mt-4" variant="destructive">{error}</Alert>}
    </div>
  );
}
