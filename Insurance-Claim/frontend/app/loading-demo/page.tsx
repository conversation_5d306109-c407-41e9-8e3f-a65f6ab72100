'use client';

import React, { useState } from 'react';
import { AuthGuard } from '@/components/auth-guard';
import { 
  SmartLoadingStates, 
  SmartLoadingOverlay, 
  SmartLoadingButton,
  SmartLoadingIndicator 
} from '@/components/ui/smart-loading';
import { useSmartLoading } from '@/lib/loading-context';
import { Skeleton, ProgressBar } from '@/components/ui/loading';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export default function LoadingDemoPage() {
  const [showOverlay, setShowOverlay] = useState(false);
  const [progress, setProgress] = useState(0);
  
  // Smart loading hooks
  const { isLoading: isLoadingData, withLoading: withLoadingData } = useSmartLoading('demo-data');
  const { isLoading: isLoadingProgress, withProgress: withProgressData } = useSmartLoading('demo-progress');
  
  // Mock data
  const [data, setData] = useState<any[]>([]);
  const [error, setError] = useState<string | null>(null);

  // Simulate loading data
  const loadData = async () => {
    await withLoadingData(async () => {
      await new Promise(resolve => setTimeout(resolve, 2000));
      setData([{ id: 1, name: 'Sample Data 1' }, { id: 2, name: 'Sample Data 2' }]);
      setError(null);
    }, 'Loading sample data...');
  };

  // Simulate loading with progress
  const loadWithProgress = async () => {
    await withProgressData(async (updateProgress) => {
      for (let i = 0; i <= 100; i += 10) {
        await new Promise(resolve => setTimeout(resolve, 200));
        updateProgress(i);
        setProgress(i);
      }
      setData([{ id: 3, name: 'Progress Data' }]);
    }, 'Loading with progress...');
  };

  // Simulate error
  const loadWithError = async () => {
    await withLoadingData(async () => {
      await new Promise(resolve => setTimeout(resolve, 1000));
      throw new Error('This is a simulated error');
    }, 'Loading with error...');
  };

  return (
    <AuthGuard>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 p-6">
        <SmartLoadingIndicator />
        
        <div className="max-w-4xl mx-auto space-y-8">
          {/* Header */}
          <div className="text-center">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Smart Loading Demo</h1>
            <p className="text-gray-600">Explore the improved loading system with better UX</p>
          </div>

          {/* Loading Controls */}
          <Card>
            <CardHeader>
              <CardTitle>Loading Controls</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-wrap gap-4">
                <SmartLoadingButton
                  onClick={loadData}
                  loading={isLoadingData}
                  variant="default"
                >
                  Load Sample Data
                </SmartLoadingButton>
                
                <SmartLoadingButton
                  onClick={loadWithProgress}
                  loading={isLoadingProgress}
                  progress={progress}
                  showProgress
                  variant="outline"
                >
                  Load with Progress
                </SmartLoadingButton>
                
                <SmartLoadingButton
                  onClick={loadWithError}
                  loading={isLoadingData}
                  variant="ghost"
                >
                  Simulate Error
                </SmartLoadingButton>
                
                <Button
                  onClick={() => setShowOverlay(!showOverlay)}
                  variant="outline"
                >
                  Toggle Overlay
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Smart Loading States Demo */}
          <Card>
            <CardHeader>
              <CardTitle>Smart Loading States</CardTitle>
            </CardHeader>
            <CardContent>
              <SmartLoadingStates
                loading={isLoadingData || isLoadingProgress}
                error={error}
                empty={data.length === 0 && !isLoadingData && !isLoadingProgress}
                loadingMessage="Loading your data..."
                emptyMessage="No data available. Click 'Load Sample Data' to get started."
              >
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Loaded Data:</h3>
                  {data.map((item) => (
                    <div key={item.id} className="p-4 bg-gray-50 rounded-lg">
                      <p className="font-medium">{item.name}</p>
                      <p className="text-sm text-gray-600">ID: {item.id}</p>
                    </div>
                  ))}
                </div>
              </SmartLoadingStates>
            </CardContent>
          </Card>

          {/* Progress Bar Demo */}
          <Card>
            <CardHeader>
              <CardTitle>Progress Bar</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <ProgressBar 
                progress={progress} 
                showPercentage 
                animated={isLoadingProgress}
              />
              <div className="text-sm text-gray-600">
                Progress: {progress}%
              </div>
            </CardContent>
          </Card>

          {/* Skeleton Demo */}
          <Card>
            <CardHeader>
              <CardTitle>Skeleton Loading</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Skeleton height="h-4" />
                <Skeleton height="h-4" />
                <Skeleton height="h-4" className="w-3/4" />
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Skeleton height="h-32" />
                <Skeleton height="h-32" />
                <Skeleton height="h-32" />
              </div>
            </CardContent>
          </Card>

          {/* Loading Overlay Demo */}
          <SmartLoadingOverlay loading={showOverlay} message="Processing overlay...">
            <Card>
              <CardHeader>
                <CardTitle>Loading Overlay Demo</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  This content will be covered by a loading overlay when you click the toggle button above.
                  The overlay provides a clean way to show loading states without disrupting the layout.
                </p>
                <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                  <p className="text-sm text-blue-800">
                    This is some sample content that will be blurred when the overlay is active.
                  </p>
                </div>
              </CardContent>
            </Card>
          </SmartLoadingOverlay>
        </div>
      </div>
    </AuthGuard>
  );
} 