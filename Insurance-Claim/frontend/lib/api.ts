// API configuration and utilities
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

// Types for API requests and responses
export interface CaseCreateRequest {
  internal_case_number?: string;
  case_name: string;
  date_of_loss?: string; // ISO date string
  date_of_lawsuit?: string; // ISO date string
  date_of_noc?: string; // ISO date string
  date_received_from_carrier: string; // ISO date string (required)
  collection_id?: string;
}

export interface CaseResponse {
  id: string;
  internal_case_number?: string;
  case_name: string;
  date_of_loss?: string;
  date_of_lawsuit?: string;
  date_of_noc?: string;
  date_received_from_carrier: string;
  collection_id?: string;
  user_id: string;
  created_at: string;
  updated_at?: string;
  files_count: number;
}

export interface CaseWithProcessingStatus extends CaseResponse {
  processing_status: 'not_started' | 'in_progress' | 'completed' | 'error';
  processing_progress: number;
  total_files: number;
  processed_files: number;
  pending_files: number;
  error_files: number;
  collection_name?: string;
}

export interface CaseFileResponse {
  id: string;
  filename: string;
  original_filename: string;
  file_size: number;
  file_type: string;
  file_path: string;
  upload_status: 'pending' | 'uploading' | 'uploaded' | 'embedding' | 'completed' | 'error';
  error_message?: string;
  case_id: string;
  user_id: string;
  created_at: string;
  updated_at?: string;
}

export interface FileUploadResponse {
  file_id: string;
  filename: string;
  status: string;
  message: string;
}

export interface CollectionCreateRequest {
  name: string;
  description?: string;
  is_expanded?: boolean;
  parent_id?: string;
}

export interface CollectionResponse {
  id: string;
  name: string;
  description?: string;
  is_expanded: boolean;
  parent_id?: string;
  user_id: string;
  created_at: string;
  updated_at?: string;
  children_count: number;
  cases_count: number;
}

export interface TreeNode {
  id: string;
  name: string;
  type: string; // 'collection' or 'case'
  expanded: boolean;
  children?: TreeNode[];
  parent_id?: string;
  case_data?: any;
}

export interface ApiError {
  detail: string;
}

// API utility functions
class ApiClient {
  private baseUrl: string;

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl;
  }

  private getAuthToken(): string | null {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('token');
    }
    return null;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    // Ensure proper URL construction by handling trailing/leading slashes
    const baseUrl = this.baseUrl.endsWith('/') ? this.baseUrl.slice(0, -1) : this.baseUrl;
    const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
    const url = `${baseUrl}${cleanEndpoint}`;
    
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      ...(options.headers as Record<string, string>),
    };

    const authToken = this.getAuthToken();
    if (authToken) {
      headers['Authorization'] = `Bearer ${authToken}`;
    }

    const response = await fetch(url, {
      ...options,
      headers,
    });

    if (!response.ok) {
      let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
      
      try {
        const errorData = await response.json();
        if (errorData.detail) {
          errorMessage = errorData.detail;
        } else if (errorData.message) {
          errorMessage = errorData.message;
        } else if (typeof errorData === 'string') {
          errorMessage = errorData;
        } else {
          errorMessage = JSON.stringify(errorData);
        }
      } catch (parseError) {
        // If we can't parse the error response, use the default message
        console.warn('Could not parse error response:', parseError);
      }
      
      throw new Error(errorMessage);
    }

    return response.json();
  }

  // Authentication methods
  async login(email: string, password: string): Promise<{access_token: string, token_type: string}> {
    return this.request<{access_token: string, token_type: string}>('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    });
  }

  async register(email: string, password: string): Promise<{msg: string}> {
    return this.request<{msg: string}>('/auth/register', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    });
  }

  // Case API methods
  async createCase(caseData: CaseCreateRequest): Promise<CaseResponse> {
    return this.request<CaseResponse>('/api/cases', {
      method: 'POST',
      body: JSON.stringify(caseData),
    });
  }

  async getCases(
    skip: number = 0,
    limit: number = 100,
    collectionId?: string
  ): Promise<CaseResponse[]> {
    const params = new URLSearchParams({
      skip: skip.toString(),
      limit: limit.toString(),
    });
    
    if (collectionId) {
      params.append('collection_id', collectionId);
    }

    return this.request<CaseResponse[]>(`/api/cases?${params}`);
  }

  async getCase(caseId: string): Promise<CaseResponse> {
    return this.request<CaseResponse>(`/api/cases/${caseId}`);
  }

  // Collection API methods
  async createCollection(collectionData: CollectionCreateRequest): Promise<CollectionResponse> {
    return this.request<CollectionResponse>('/api/collections', {
      method: 'POST',
      body: JSON.stringify(collectionData),
    });
  }

  async getCollections(): Promise<CollectionResponse[]> {
    return this.request<CollectionResponse[]>('/api/collections');
  }

  // Tree structure API method
  async getTreeStructure(): Promise<TreeNode[]> {
    return this.request<TreeNode[]>('/api/tree');
  }

  // Get all cases with processing status
  async getCasesWithStatus(skip: number = 0, limit: number = 100): Promise<CaseWithProcessingStatus[]> {
    return this.request<CaseWithProcessingStatus[]>(`/api/cases/with-status?skip=${skip}&limit=${limit}`);
  }

  // File upload methods
  async uploadFile(caseId: string, file: File, onProgress?: (progress: number) => void): Promise<FileUploadResponse> {
    const formData = new FormData();
    formData.append('file', file);

    const headers: Record<string, string> = {};
    const authToken = this.getAuthToken();
    if (authToken) {
      headers['Authorization'] = `Bearer ${authToken}`;
    }

    // Use XMLHttpRequest for progress tracking
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();

      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable && onProgress) {
          const progress = Math.round((event.loaded / event.total) * 100);
          onProgress(progress);
        }
      });

      xhr.addEventListener('load', () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          try {
            const response = JSON.parse(xhr.responseText);
            resolve(response);
          } catch (error) {
            reject(new Error('Invalid response format'));
          }
        } else {
          try {
            const errorData = JSON.parse(xhr.responseText);
            reject(new Error(errorData.detail || `HTTP ${xhr.status}: ${xhr.statusText}`));
          } catch {
            reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));
          }
        }
      });

      xhr.addEventListener('error', () => {
        reject(new Error('Network error occurred'));
      });

      xhr.open('POST', `${this.baseUrl}api/cases/${caseId}/files`);

      // Set headers
      Object.entries(headers).forEach(([key, value]) => {
        xhr.setRequestHeader(key, value);
      });
      console.log('DEBUG API CLIENT: Sending formData:', file);
      xhr.send(formData);
    });
  }

  async uploadFilesOnly(caseId: string, files: File[], onProgress?: (progress: number) => void): Promise<FileUploadResponse[]> {
    const formData = new FormData();
    files.forEach(file => {
      formData.append('files', file);
    });

    const headers: Record<string, string> = {};
    const authToken = this.getAuthToken();
    if (authToken) {
      headers['Authorization'] = `Bearer ${authToken}`;
    }

    // Use XMLHttpRequest for progress tracking
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();

      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable && onProgress) {
          const progress = Math.round((event.loaded / event.total) * 100);
          onProgress(progress);
        }
      });

      xhr.addEventListener('load', () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          try {
            const response = JSON.parse(xhr.responseText);
            resolve(response);
          } catch (error) {
            reject(new Error('Invalid response format'));
          }
        } else {
          try {
            const errorData = JSON.parse(xhr.responseText);
            reject(new Error(errorData.detail || `HTTP ${xhr.status}: ${xhr.statusText}`));
          } catch {
            reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));
          }
        }
      });

      xhr.addEventListener('error', () => {
        reject(new Error('Network error occurred'));
      });

      xhr.open('POST', `${this.baseUrl}api/cases/${caseId}/files/upload-only`);

      // Set headers
      Object.entries(headers).forEach(([key, value]) => {
        xhr.setRequestHeader(key, value);
      });
      console.log('DEBUG API CLIENT: Sending upload-only formData with', files.length, 'files');
      xhr.send(formData);
    });
  }

  async uploadFilesBatch(caseId: string, files: File[], onProgress?: (progress: number) => void): Promise<FileUploadResponse[]> {
    const formData = new FormData();
    files.forEach(file => {
      formData.append('files', file);
    });

    const headers: Record<string, string> = {};
    const authToken = this.getAuthToken();
    if (authToken) {
      headers['Authorization'] = `Bearer ${authToken}`;
    }

    // Use XMLHttpRequest for progress tracking
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();

      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable && onProgress) {
          const progress = Math.round((event.loaded / event.total) * 100);
          onProgress(progress);
        }
      });

      xhr.addEventListener('load', () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          try {
            const response = JSON.parse(xhr.responseText);
            resolve(response);
          } catch (error) {
            reject(new Error('Invalid response format'));
          }
        } else {
          try {
            const errorData = JSON.parse(xhr.responseText);
            reject(new Error(errorData.detail || `HTTP ${xhr.status}: ${xhr.statusText}`));
          } catch {
            reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));
          }
        }
      });

      xhr.addEventListener('error', () => {
        reject(new Error('Network error occurred'));
      });

      xhr.open('POST', `${this.baseUrl}api/cases/${caseId}/files/batch`);

      // Set headers
      Object.entries(headers).forEach(([key, value]) => {
        xhr.setRequestHeader(key, value);
      });
      console.log('DEBUG API CLIENT: Sending batch formData with', files.length, 'files');
      xhr.send(formData);
    });
  }

  async processEmbeddings(caseId: string): Promise<{message: string, processed_count: number, status: string}> {
    return this.request<{message: string, processed_count: number, status: string}>(`/api/cases/${caseId}/files/process-embeddings`, {
      method: 'POST',
    });
  }

  async getCaseFiles(caseId: string): Promise<CaseFileResponse[]> {
    return this.request<CaseFileResponse[]>(`/api/cases/${caseId}/files`);
  }

  async deleteCaseFile(caseId: string, fileId: string): Promise<void> {
    await this.request(`/api/cases/${caseId}/files/${fileId}`, {
      method: 'DELETE',
    });
  }

  // Move case API method
  async moveCase(caseId: string, collectionId?: string): Promise<void> {
    console.log('DEBUG API CLIENT: moveCase called with:', { caseId, collectionId });

    const formData = new FormData();
    // Always append collection_id, even if it's empty (for moving to root)
    formData.append('collection_id', collectionId || '');

    // Debug: Log what's actually in the FormData
    console.log('DEBUG API CLIENT: FormData contents:');
    console.log(`  collection_id: ${formData.get('collection_id')}`);
    console.log('DEBUG API CLIENT: collectionId value being sent:', collectionId);

    const headers: Record<string, string> = {};
    const authToken = this.getAuthToken();
    if (authToken) {
      headers['Authorization'] = `Bearer ${authToken}`;
    }

    console.log('DEBUG API CLIENT: Making request to:', `/api/cases/${caseId}/move`);

    // Use the custom request method but override headers for FormData
    const url = `${this.baseUrl}api/cases/${caseId}/move`;

    const response = await fetch(url, {
      method: 'PUT',
      headers, // Don't set Content-Type for FormData - let browser set it
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({
        detail: `HTTP ${response.status}: ${response.statusText}`,
      }));
      throw new Error(errorData.detail);
    }
  }
}

// Export a singleton instance
export const apiClient = new ApiClient();

// Utility functions for date conversion
export const formatDateForApi = (dateString: string): string | undefined => {
  if (!dateString) return undefined;
  
  // Convert from MM/DD/YYYY to YYYY-MM-DD
  const parts = dateString.split('/');
  if (parts.length === 3) {
    const [month, day, year] = parts;
    return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
  }
  
  return dateString; // Return as-is if already in correct format
};

export const formatDateFromApi = (dateString: string): string => {
  if (!dateString) return '';
  
  // Convert from YYYY-MM-DD to MM/DD/YYYY
  const date = new Date(dateString);
  if (isNaN(date.getTime())) return dateString;
  
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const year = date.getFullYear();
  
  return `${month}/${day}/${year}`;
};
