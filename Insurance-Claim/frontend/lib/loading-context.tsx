'use client';

import React, { createContext, useContext, useState, useCallback } from 'react';

interface LoadingState {
  id: string;
  message?: string;
  progress?: number;
}

interface LoadingContextType {
  loadingStates: LoadingState[];
  startLoading: (id: string, message?: string) => void;
  stopLoading: (id: string) => void;
  updateProgress: (id: string, progress: number) => void;
  isLoading: (id: string) => boolean;
  hasAnyLoading: boolean;
  clearAll: () => void;
}

const LoadingContext = createContext<LoadingContextType | undefined>(undefined);

export function LoadingProvider({ children }: { children: React.ReactNode }) {
  const [loadingStates, setLoadingStates] = useState<LoadingState[]>([]);

  const startLoading = useCallback((id: string, message?: string) => {
    setLoadingStates(prev => {
      const exists = prev.find(state => state.id === id);
      if (exists) {
        return prev.map(state => 
          state.id === id ? { ...state, message } : state
        );
      }
      return [...prev, { id, message, progress: 0 }];
    });
  }, []);

  const stopLoading = useCallback((id: string) => {
    setLoadingStates(prev => prev.filter(state => state.id !== id));
  }, []);

  const updateProgress = useCallback((id: string, progress: number) => {
    setLoadingStates(prev => 
      prev.map(state => 
        state.id === id ? { ...state, progress } : state
      )
    );
  }, []);

  const isLoading = useCallback((id: string) => {
    return loadingStates.some(state => state.id === id);
  }, [loadingStates]);

  const hasAnyLoading = loadingStates.length > 0;

  const clearAll = useCallback(() => {
    setLoadingStates([]);
  }, []);

  return (
    <LoadingContext.Provider value={{
      loadingStates,
      startLoading,
      stopLoading,
      updateProgress,
      isLoading,
      hasAnyLoading,
      clearAll
    }}>
      {children}
    </LoadingContext.Provider>
  );
}

export function useLoadingContext() {
  const context = useContext(LoadingContext);
  if (context === undefined) {
    throw new Error('useLoadingContext must be used within a LoadingProvider');
  }
  return context;
}

// Smart loading hook that integrates with the context
export function useSmartLoading(id: string) {
  const { startLoading, stopLoading, updateProgress, isLoading } = useLoadingContext();

  const withLoading = useCallback(async <T,>(
    asyncFn: () => Promise<T>,
    message?: string
  ): Promise<T | null> => {
    startLoading(id, message);
    
    try {
      const result = await asyncFn();
      return result;
    } catch (error) {
      console.error(`Error in loading operation ${id}:`, error);
      return null;
    } finally {
      stopLoading(id);
    }
  }, [id, startLoading, stopLoading]);

  const withProgress = useCallback(async <T,>(
    asyncFn: (updateProgress: (progress: number) => void) => Promise<T>,
    message?: string
  ): Promise<T | null> => {
    startLoading(id, message);
    
    try {
      const result = await asyncFn((progress: number) => updateProgress(id, progress));
      return result;
    } catch (error) {
      console.error(`Error in loading operation ${id}:`, error);
      return null;
    } finally {
      stopLoading(id);
    }
  }, [id, startLoading, stopLoading, updateProgress]);

  return {
    isLoading: isLoading(id),
    withLoading,
    withProgress,
    updateProgress: (progress: number) => updateProgress(id, progress)
  };
} 