'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { useLoadingContext } from '@/lib/loading-context';
import { Spinner, ProgressBar } from './loading';

// Smart Loading Indicator
export function SmartLoadingIndicator() {
  const { loadingStates, hasAnyLoading } = useLoadingContext();

  if (!hasAnyLoading) return null;

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2 max-w-sm">
      {loadingStates.map((state) => (
        <LoadingCard key={state.id} state={state} />
      ))}
    </div>
  );
}

// Individual Loading Card
function LoadingCard({ state }: { state: { id: string; message?: string; progress?: number } }) {
  const { stopLoading } = useLoadingContext();

  return (
    <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-4 animate-in slide-in-from-right-4 duration-300">
      <div className="flex items-start space-x-3">
        <Spinner size="sm" className="mt-0.5 flex-shrink-0" />
        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium text-gray-900">
            {state.message || 'Loading...'}
          </p>
          {state.progress !== undefined && (
            <div className="mt-2">
              <ProgressBar 
                progress={state.progress} 
                showPercentage 
                className="h-1.5"
              />
            </div>
          )}
        </div>
        <button
          onClick={() => stopLoading(state.id)}
          className="text-gray-400 hover:text-gray-600 transition-colors"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
    </div>
  );
}

// Smart Loading States Component
interface SmartLoadingStatesProps {
  loading: boolean;
  error?: string | null;
  empty?: boolean;
  emptyMessage?: string;
  loadingMessage?: string;
  children: React.ReactNode;
  className?: string;
  showSpinner?: boolean;
}

export function SmartLoadingStates({
  loading,
  error,
  empty = false,
  emptyMessage = 'No data available',
  loadingMessage = 'Loading...',
  children,
  className,
  showSpinner = true
}: SmartLoadingStatesProps) {
  if (loading) {
    return (
      <div className={cn('flex items-center justify-center py-12', className)}>
        <div className="text-center">
          {showSpinner && <Spinner size="lg" className="mx-auto mb-4" />}
          <p className="text-sm text-gray-600">{loadingMessage}</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={cn('flex items-center justify-center py-12', className)}>
        <div className="text-center">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Something went wrong</h3>
          <p className="text-sm text-red-600 max-w-md">{error}</p>
        </div>
      </div>
    );
  }

  if (empty) {
    return (
      <div className={cn('flex items-center justify-center py-12', className)}>
        <div className="text-center">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No data found</h3>
          <p className="text-sm text-gray-500">{emptyMessage}</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}

// Smart Loading Overlay
interface SmartLoadingOverlayProps {
  loading: boolean;
  children: React.ReactNode;
  message?: string;
  className?: string;
  blur?: boolean;
}

export function SmartLoadingOverlay({
  loading,
  children,
  message = 'Loading...',
  className,
  blur = true
}: SmartLoadingOverlayProps) {
  if (!loading) return <>{children}</>;

  return (
    <div className={cn('relative', className)}>
      {children}
      <div className={cn(
        'absolute inset-0 flex items-center justify-center z-10',
        blur ? 'bg-white/80 backdrop-blur-sm' : 'bg-white/90'
      )}>
        <div className="text-center">
          <Spinner size="lg" className="mx-auto mb-3" />
          <p className="text-sm text-gray-600">{message}</p>
        </div>
      </div>
    </div>
  );
}

// Smart Loading Button with Progress
interface SmartLoadingButtonProps {
  children: React.ReactNode;
  loading?: boolean;
  progress?: number;
  disabled?: boolean;
  className?: string;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  showProgress?: boolean;
}

export function SmartLoadingButton({
  children,
  loading = false,
  progress,
  disabled = false,
  className,
  onClick,
  type = 'button',
  variant = 'default',
  size = 'md',
  showProgress = false
}: SmartLoadingButtonProps) {
  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 relative overflow-hidden';
  
  const variantClasses = {
    default: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 disabled:bg-blue-400',
    outline: 'border border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-gray-500 disabled:bg-gray-100',
    ghost: 'text-gray-700 hover:bg-gray-100 focus:ring-gray-500 disabled:bg-gray-50'
  };

  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-sm',
    lg: 'px-6 py-3 text-base'
  };

  return (
    <button
      type={type}
      disabled={disabled || loading}
      onClick={onClick}
      className={cn(
        baseClasses,
        variantClasses[variant],
        sizeClasses[size],
        loading && 'cursor-not-allowed',
        className
      )}
    >
      {showProgress && progress !== undefined && (
        <div 
          className="absolute inset-0 bg-blue-500/20 transition-all duration-300"
          style={{ width: `${progress}%` }}
        />
      )}
      <div className="relative flex items-center">
        {loading && (
          <Spinner size="sm" className="mr-2" color={variant === 'default' ? 'white' : 'gray'} />
        )}
        {children}
      </div>
    </button>
  );
} 